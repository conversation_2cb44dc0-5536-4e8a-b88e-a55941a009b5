services:
  transmission:
    image: linuxserver/transmission:4.0.6
    container_name: transmission
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${PATH_CONFIG}/transmission:/config
      - ${PATH_STORAGE}/downloads:/downloads
      - ${PATH_WATCH}:/watch
    ports:
      - 9091:9091
      - 51413:51413
      - 51413:51413/udp
    restart: unless-stopped

  prowlarr:
    image: linuxserver/prowlarr:1.29.2
    container_name: prowlarr
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${PATH_CONFIG}/prowlarr:/config
    ports:
      - 9696:9696
    restart: unless-stopped

  # tv shows
  sonarr:
    image: linuxserver/sonarr:4.0.12
    container_name: sonarr
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${PATH_CONFIG}/sonarr:/config
      - ${PATH_STORAGE}/tv:/tv # Location of TV library on disk
      - ${PATH_STORAGE}/downloads:/downloads # Location of download managers output directory
    ports:
      - 8989:8989
    restart: unless-stopped

  # movies
  radarr:
    image: linuxserver/radarr:5.17.2
    container_name: radarr
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${PATH_CONFIG}/radarr:/config
      - ${PATH_STORAGE}/movies:/movies # Location of Movie library on disk
      - ${PATH_STORAGE}/downloads:/downloads # Location of download managers output directory
    ports:
      - 7878:7878
    restart: unless-stopped

  # subtitles
  bazarr:
    image: linuxserver/bazarr:1.5.1
    container_name: bazarr
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${PATH_CONFIG}/bazarr:/config
      - ${PATH_STORAGE}/movies:/movies
      - ${PATH_STORAGE}/tv:/tv
    ports:
      - 6767:6767
    restart: unless-stopped

  # media server
  jellyfin:
    image: linuxserver/jellyfin:10.10.3
    container_name: jellyfin
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${PATH_CONFIG}/jellyfin:/config
      - ${PATH_STORAGE}/tv:/data/tvshows
      - ${PATH_STORAGE}/movies:/data/movies
    ports:
      - 8096:8096
    restart: unless-stopped

  flaresolverr:
    image: flaresolverr/flaresolverr:v3.3.21
    container_name: flaresolverr
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_HTML=${LOG_HTML:-false}
      - CAPTCHA_SOLVER=${CAPTCHA_SOLVER:-none}
      - TZ=${TZ}
    ports:
      - ${PORT:-8191}:8191
    restart: unless-stopped

  recyclarr:
    image: recyclarr/recyclarr:7.4.0
    container_name: recyclarr
    environment:
      - TZ=${TZ}
    user: ${PUID}:${PGID}
    volumes:
      - ${PATH_CONFIG}/recyclarr:/config

  unpackerr:
    image: golift/unpackerr:0.14.5
    container_name: unpackerr
    environment:
      - TZ=${TZ}
      - UN_SONARR_0_URL=http://sonarr:8989
      - UN_SONARR_0_API_KEY=${API_KEY_SONARR}
      - UN_RADARR_0_URL=http://radarr:7878
      - UN_RADARR_0_API_KEY=${API_KEY_RADARR}
    user: ${PUID}:${PGID}
    volumes:
      - ${PATH_STORAGE}/downloads:/downloads
